<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>闲鱼收益监控器 - 调试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .qr-preview {
            max-width: 200px;
            max-height: 200px;
            border: 1px solid #ddd;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 闲鱼收益监控器 - 调试页面</h1>
        
        <div class="section">
            <h3>1. 服务器状态检查</h3>
            <button class="btn" onclick="checkServerHealth()">检查服务器状态</button>
            <div id="serverStatus" class="result"></div>
        </div>
        
        <div class="section">
            <h3>2. 获取登录页面</h3>
            <button class="btn" onclick="testLoginPage()">测试登录页面获取</button>
            <div id="loginPageResult" class="result"></div>
            <img id="qrPreview" class="qr-preview" style="display: none;" alt="二维码预览">
        </div>
        
        <div class="section">
            <h3>3. 登录状态检查</h3>
            <input type="text" id="sessionIdInput" placeholder="输入会话ID" style="width: 300px; padding: 8px; margin-right: 10px;">
            <button class="btn" onclick="testLoginStatus()">检查登录状态</button>
            <div id="loginStatusResult" class="result"></div>
        </div>
        
        <div class="section">
            <h3>4. 收益数据获取</h3>
            <button class="btn" onclick="testEarningsData()">测试收益数据获取</button>
            <div id="earningsResult" class="result"></div>
        </div>
        
        <div class="section">
            <h3>5. 直接API测试</h3>
            <input type="text" id="apiUrlInput" placeholder="输入API URL" value="https://www.goofish.pro/login" style="width: 400px; padding: 8px; margin-right: 10px;">
            <button class="btn" onclick="testDirectAPI()">直接测试API</button>
            <div id="directApiResult" class="result"></div>
        </div>
        
        <div class="section">
            <h3>6. 系统信息</h3>
            <div id="systemInfo" class="result"></div>
        </div>
    </div>

    <script>
        let currentSessionId = null;
        
        // 显示系统信息
        document.getElementById('systemInfo').textContent = JSON.stringify({
            userAgent: navigator.userAgent,
            language: navigator.language,
            platform: navigator.platform,
            cookieEnabled: navigator.cookieEnabled,
            onLine: navigator.onLine,
            url: window.location.href
        }, null, 2);
        
        async function checkServerHealth() {
            const resultEl = document.getElementById('serverStatus');
            try {
                resultEl.textContent = '正在检查服务器状态...';
                
                const response = await fetch('/api/health');
                const data = await response.json();
                
                resultEl.textContent = JSON.stringify(data, null, 2);
                showStatus('服务器状态检查完成', 'success');
            } catch (error) {
                resultEl.textContent = `错误: ${error.message}`;
                showStatus('服务器状态检查失败', 'error');
            }
        }
        
        async function testLoginPage() {
            const resultEl = document.getElementById('loginPageResult');
            const qrPreview = document.getElementById('qrPreview');
            
            try {
                resultEl.textContent = '正在获取登录页面...';
                
                const response = await fetch('/api/proxy/login');
                const data = await response.json();
                
                resultEl.textContent = JSON.stringify(data, null, 2);
                
                if (data.success && data.qrCode) {
                    qrPreview.src = data.qrCode;
                    qrPreview.style.display = 'block';
                    currentSessionId = data.sessionId;
                    document.getElementById('sessionIdInput').value = currentSessionId;
                    showStatus('二维码获取成功', 'success');
                } else {
                    qrPreview.style.display = 'none';
                    showStatus('二维码获取失败', 'error');
                }
            } catch (error) {
                resultEl.textContent = `错误: ${error.message}`;
                qrPreview.style.display = 'none';
                showStatus('登录页面测试失败', 'error');
            }
        }
        
        async function testLoginStatus() {
            const resultEl = document.getElementById('loginStatusResult');
            const sessionId = document.getElementById('sessionIdInput').value || currentSessionId;
            
            if (!sessionId) {
                resultEl.textContent = '请先获取会话ID或手动输入';
                return;
            }
            
            try {
                resultEl.textContent = '正在检查登录状态...';
                
                const response = await fetch('/api/check-login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ sessionId: sessionId })
                });
                
                const data = await response.json();
                resultEl.textContent = JSON.stringify(data, null, 2);
                
                if (data.success && data.isLoggedIn) {
                    showStatus('已登录', 'success');
                } else {
                    showStatus('未登录', 'info');
                }
            } catch (error) {
                resultEl.textContent = `错误: ${error.message}`;
                showStatus('登录状态检查失败', 'error');
            }
        }
        
        async function testEarningsData() {
            const resultEl = document.getElementById('earningsResult');
            const sessionId = document.getElementById('sessionIdInput').value || currentSessionId;
            
            if (!sessionId) {
                resultEl.textContent = '请先获取会话ID或手动输入';
                return;
            }
            
            try {
                resultEl.textContent = '正在获取收益数据...';
                
                const response = await fetch(`/api/proxy/earnings?sessionId=${sessionId}`);
                const data = await response.json();
                
                resultEl.textContent = JSON.stringify(data, null, 2);
                
                if (data.success) {
                    showStatus('收益数据获取成功', 'success');
                } else {
                    showStatus('收益数据获取失败', 'error');
                }
            } catch (error) {
                resultEl.textContent = `错误: ${error.message}`;
                showStatus('收益数据测试失败', 'error');
            }
        }
        
        async function testDirectAPI() {
            const resultEl = document.getElementById('directApiResult');
            const apiUrl = document.getElementById('apiUrlInput').value;
            
            try {
                resultEl.textContent = '正在测试直接API调用...';
                
                const response = await fetch(apiUrl, {
                    mode: 'no-cors' // 尝试绕过CORS
                });
                
                resultEl.textContent = `状态: ${response.status}\n类型: ${response.type}\n\n注意: 由于CORS限制，可能无法获取响应内容`;
                
                showStatus('直接API测试完成（可能受CORS限制）', 'info');
            } catch (error) {
                resultEl.textContent = `错误: ${error.message}\n\n这是预期的，因为存在CORS限制`;
                showStatus('直接API调用失败（CORS限制）', 'error');
            }
        }
        
        function showStatus(message, type) {
            // 创建状态提示
            const statusEl = document.createElement('div');
            statusEl.className = `status ${type}`;
            statusEl.textContent = message;
            
            // 插入到页面顶部
            const container = document.querySelector('.container');
            container.insertBefore(statusEl, container.firstChild);
            
            // 3秒后自动移除
            setTimeout(() => {
                if (statusEl.parentNode) {
                    statusEl.parentNode.removeChild(statusEl);
                }
            }, 3000);
        }
        
        // 自动检查服务器状态
        window.addEventListener('load', () => {
            checkServerHealth();
        });
    </script>
</body>
</html>
