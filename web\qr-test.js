const https = require('https');

// 发送HTTPS请求的辅助函数
function makeHttpsRequest(options) {
    return new Promise((resolve, reject) => {
        const req = https.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                resolve({
                    statusCode: res.statusCode,
                    headers: res.headers,
                    data: data
                });
            });
        });
        
        req.on('error', (error) => {
            reject(error);
        });
        
        req.setTimeout(10000, () => {
            req.destroy();
            reject(new Error('请求超时'));
        });
        
        req.end();
    });
}

// 测试二维码API
async function testQRCodeAPI() {
    console.log('=== 测试二维码API ===');
    
    const options = {
        hostname: 'www.goofish.pro',
        port: 443,
        path: '/api/user/login/dingQrcode',
        method: 'GET',
        headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Referer': 'https://www.goofish.pro/login',
            'Origin': 'https://www.goofish.pro'
        }
    };
    
    try {
        const response = await makeHttpsRequest(options);
        console.log('状态码:', response.statusCode);
        console.log('响应长度:', response.data.length);
        console.log('响应内容:', response.data);
        
        if (response.statusCode === 200) {
            try {
                const data = JSON.parse(response.data);
                console.log('✅ 解析成功');
                console.log('数据结构:', Object.keys(data));
                
                if (data.data && typeof data.data === 'string' && data.data.startsWith('data:image')) {
                    console.log('🎉 找到二维码数据！');
                    console.log('二维码长度:', data.data.length);
                    return data.data;
                }
                
                return data;
            } catch (parseError) {
                console.log('❌ JSON解析失败:', parseError.message);
            }
        }
        
    } catch (error) {
        console.error('❌ API调用失败:', error.message);
    }
    
    return null;
}

// 测试其他可能的二维码API
async function testOtherQRAPIs() {
    console.log('\n=== 测试其他可能的二维码API ===');
    
    const apis = [
        '/api/user/login/qrcode',
        '/api/login/qrcode',
        '/api/qrcode',
        '/api/auth/qrcode',
        '/api/user/qrcode'
    ];
    
    for (const api of apis) {
        console.log(`\n测试: ${api}`);
        
        const options = {
            hostname: 'www.goofish.pro',
            port: 443,
            path: api,
            method: 'GET',
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'application/json, text/plain, */*',
                'Referer': 'https://www.goofish.pro/login'
            }
        };
        
        try {
            const response = await makeHttpsRequest(options);
            console.log(`${api} - 状态码:`, response.statusCode);
            
            if (response.statusCode === 200) {
                console.log(`${api} - 响应:`, response.data.substring(0, 200));
            }
            
        } catch (error) {
            console.log(`${api} - 错误:`, error.message);
        }
    }
}

// 测试POST请求获取二维码
async function testQRCodePOST() {
    console.log('\n=== 测试POST请求获取二维码 ===');
    
    const options = {
        hostname: 'www.goofish.pro',
        port: 443,
        path: '/api/user/login/dingQrcode',
        method: 'POST',
        headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Content-Type': 'application/json',
            'Referer': 'https://www.goofish.pro/login',
            'Origin': 'https://www.goofish.pro'
        }
    };
    
    return new Promise((resolve, reject) => {
        const req = https.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                console.log('POST状态码:', res.statusCode);
                console.log('POST响应:', data);
                
                if (res.statusCode === 200) {
                    try {
                        const jsonData = JSON.parse(data);
                        console.log('✅ POST解析成功');
                        resolve(jsonData);
                    } catch (error) {
                        console.log('❌ POST JSON解析失败');
                        resolve(null);
                    }
                } else {
                    resolve(null);
                }
            });
        });
        
        req.on('error', (error) => {
            console.log('❌ POST请求失败:', error.message);
            resolve(null);
        });
        
        // 发送空的JSON数据
        req.write('{}');
        req.end();
    });
}

// 主函数
async function main() {
    console.log('开始测试闲管家二维码API...\n');
    
    // 测试GET请求
    const qrData = await testQRCodeAPI();
    
    // 测试POST请求
    await testQRCodePOST();
    
    // 测试其他可能的API
    await testOtherQRAPIs();
    
    console.log('\n测试完成！');
    
    if (qrData) {
        console.log('\n🎉 成功获取到二维码数据！');
        if (typeof qrData === 'string' && qrData.startsWith('data:image')) {
            console.log('二维码类型: Base64图片');
            console.log('二维码长度:', qrData.length);
        }
    } else {
        console.log('\n❌ 未能获取到二维码数据');
    }
}

// 运行测试
main().catch(console.error);
