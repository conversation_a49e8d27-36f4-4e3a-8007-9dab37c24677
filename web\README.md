# 闲鱼收益监控器

一个用于实时监控闲鱼收益数据的网页应用程序。

## 功能特性

- 🔐 **二维码登录**: 获取闲管家登录页面的二维码，支持扫码登录
- 📊 **实时数据**: 获取订单总数和总收益数据
- 🎨 **美观界面**: 现代化的响应式设计
- 🔄 **自动刷新**: 支持手动和自动刷新数据
- 🛡️ **跨域处理**: 内置代理服务器解决跨域问题

## 技术架构

### 前端
- **HTML5 + CSS3**: 响应式界面设计
- **JavaScript (ES6+)**: 异步数据处理和DOM操作
- **Fetch API**: 网络请求处理

### 后端
- **Node.js + Express**: 代理服务器
- **Axios**: HTTP客户端
- **CORS**: 跨域资源共享处理

## 安装和运行

### 1. 安装依赖

```bash
cd web
npm install
```

### 2. 启动服务器

```bash
npm start
```

或者使用开发模式（自动重启）：

```bash
npm run dev
```

### 3. 访问应用

打开浏览器访问：`http://localhost:3000`

## 使用说明

### 登录流程

1. 点击"获取登录二维码"按钮
2. 等待二维码加载完成
3. 使用闲鱼APP扫描二维码
4. 登录成功后自动跳转到数据页面

### 数据监控

- **订单总数**: 显示当前总订单数量
- **总收益**: 显示累计收益金额
- **刷新数据**: 点击刷新按钮获取最新数据

## API接口

### 获取登录二维码
```
GET /api/proxy/login
```

### 检查登录状态
```
POST /api/check-login
Body: { "sessionId": "session_id" }
```

### 获取收益数据
```
GET /api/proxy/earnings?sessionId=session_id
```

### 获取会话ID
```
GET /api/session
```

## 目标API说明

本应用主要与以下闲管家API进行交互：

1. **登录页面**: `https://www.goofish.pro/login`
   - 获取登录二维码（查找 `data:image/png;base64` 开头的URL）

2. **收益数据API**: `https://api.goofish.pro/api/stats/order`
   - 参数：
     - `channel=1`
     - `version=3.54.31`
     - `authorize_id=0`
     - `date_type=1`
     - `selectIndex=0`
   - 返回数据中第一个数组元素的 `num`（订单总数）和 `account`（总收益）

## 注意事项

### 跨域问题
由于浏览器的同源策略限制，直接从前端访问闲管家API会遇到跨域问题。本项目通过以下方式解决：

1. **代理服务器**: 使用Node.js Express服务器作为代理
2. **CORS配置**: 在代理服务器中配置CORS头
3. **会话管理**: 维护用户登录状态和Cookie

### 安全考虑
- 不在前端存储敏感信息
- 使用会话ID管理用户状态
- 代理服务器处理所有外部API调用

### 限制说明
- 需要有效的闲鱼账号
- 依赖闲管家网站的可用性
- API接口可能随时变更

## 开发计划

- [ ] 添加数据持久化存储
- [ ] 实现数据图表展示
- [ ] 添加数据导出功能
- [ ] 支持多账号管理
- [ ] 移动端适配优化

## 故障排除

### 常见问题

1. **二维码获取失败**
   - 检查网络连接
   - 确认闲管家网站可访问
   - 查看控制台错误信息

2. **登录状态检查失败**
   - 确认会话ID有效
   - 检查代理服务器运行状态

3. **数据获取失败**
   - 确认已成功登录
   - 检查API接口是否变更
   - 查看服务器日志

### 调试模式

启动服务器时会在控制台输出详细日志，包括：
- API请求状态
- 错误信息
- 数据处理过程

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。
