const express = require('express');
const cors = require('cors');
const axios = require('axios');
const path = require('path');
const config = require('./config');

const app = express();
const PORT = config.server.port;

// 启用CORS
app.use(cors());
app.use(express.json());

// 提供静态文件服务
app.use(express.static(path.join(__dirname)));

// 存储用户会话信息
const sessions = new Map();

// 代理登录页面请求
app.get('/api/proxy/login', async (req, res) => {
    try {
        if (config.debug.enabled) {
            console.log('正在获取登录页面...');
        }

        const response = await axios.get(config.target.loginUrl, {
            headers: {
                'User-Agent': config.request.userAgent,
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                ...config.request.headers,
                'Upgrade-Insecure-Requests': '1'
            },
            timeout: config.request.timeout
        });

        const html = response.data;
        
        // 查找二维码
        const qrCodeMatch = html.match(/data:image\/png;base64,[A-Za-z0-9+/=]+/);
        
        if (qrCodeMatch) {
            res.json({
                success: true,
                qrCode: qrCodeMatch[0],
                message: '二维码获取成功'
            });
        } else {
            // 尝试查找其他可能的二维码格式
            const imgMatches = html.match(/<img[^>]+src="([^"]*qr[^"]*)"[^>]*>/gi);
            
            if (imgMatches && imgMatches.length > 0) {
                // 提取第一个可能的二维码图片URL
                const srcMatch = imgMatches[0].match(/src="([^"]*)"/);
                if (srcMatch) {
                    res.json({
                        success: true,
                        qrCode: srcMatch[1],
                        message: '二维码URL获取成功'
                    });
                    return;
                }
            }
            
            res.json({
                success: false,
                message: '未找到二维码',
                html: html.substring(0, 1000) // 返回部分HTML用于调试
            });
        }

    } catch (error) {
        console.error('获取登录页面失败:', error.message);
        res.status(500).json({
            success: false,
            message: `获取登录页面失败: ${error.message}`
        });
    }
});

// 检查登录状态
app.post('/api/check-login', async (req, res) => {
    try {
        const { sessionId } = req.body;
        
        // 实际项目中这里应该检查真实的登录状态
        // 暂时使用模拟逻辑
        const isLoggedIn = Math.random() > 0.7; // 30%的概率返回已登录
        
        if (isLoggedIn) {
            // 模拟保存会话信息
            sessions.set(sessionId, {
                loggedIn: true,
                cookies: 'mock_session_cookie=12345',
                loginTime: new Date()
            });
        }
        
        res.json({
            success: true,
            isLoggedIn: isLoggedIn,
            message: isLoggedIn ? '登录成功' : '等待登录'
        });

    } catch (error) {
        console.error('检查登录状态失败:', error.message);
        res.status(500).json({
            success: false,
            message: `检查登录状态失败: ${error.message}`
        });
    }
});

// 代理收益数据请求
app.get('/api/proxy/earnings', async (req, res) => {
    try {
        const { sessionId } = req.query;
        const session = sessions.get(sessionId);
        
        if (!session || !session.loggedIn) {
            return res.status(401).json({
                success: false,
                message: '请先登录'
            });
        }

        console.log('正在获取收益数据...');
        
        // 实际的API调用
        const apiUrl = 'https://api.goofish.pro/api/stats/order';
        const params = {
            channel: 1,
            version: '3.54.31',
            authorize_id: 0,
            date_type: 1,
            selectIndex: 0
        };

        try {
            const response = await axios.get(apiUrl, {
                params: params,
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'Accept': 'application/json, text/plain, */*',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'Cookie': session.cookies,
                    'Referer': 'https://www.goofish.pro/',
                    'Origin': 'https://www.goofish.pro'
                },
                timeout: 10000
            });

            if (response.data && response.data.data && response.data.data.length > 0) {
                const firstItem = response.data.data[0];
                res.json({
                    success: true,
                    data: {
                        num: firstItem.num,
                        account: firstItem.account
                    },
                    message: '数据获取成功'
                });
            } else {
                throw new Error('数据格式不正确');
            }

        } catch (apiError) {
            console.log('API调用失败，返回模拟数据:', apiError.message);
            
            // 返回模拟数据
            const simulatedData = {
                num: Math.floor(Math.random() * 1000) + 100,
                account: (Math.random() * 10000 + 1000).toFixed(2)
            };
            
            res.json({
                success: true,
                data: simulatedData,
                message: '返回模拟数据（API调用失败）'
            });
        }

    } catch (error) {
        console.error('获取收益数据失败:', error.message);
        res.status(500).json({
            success: false,
            message: `获取收益数据失败: ${error.message}`
        });
    }
});

// 生成会话ID
app.get('/api/session', (req, res) => {
    const sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    res.json({
        success: true,
        sessionId: sessionId
    });
});

// 健康检查
app.get('/api/health', (req, res) => {
    res.json({
        success: true,
        message: '服务器运行正常',
        timestamp: new Date().toISOString()
    });
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`代理服务器运行在 http://localhost:${PORT}`);
    console.log('请在浏览器中访问 http://localhost:3000 来使用闲鱼收益监控器');
});

module.exports = app;
