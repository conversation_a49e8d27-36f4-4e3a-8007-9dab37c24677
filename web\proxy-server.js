const express = require('express');
const cors = require('cors');
const axios = require('axios');
const path = require('path');
const config = require('./config');
const cheerio = require('cheerio');

const app = express();
const PORT = config.server.port;

// 启用CORS
app.use(cors());
app.use(express.json());

// 提供静态文件服务
app.use(express.static(path.join(__dirname)));

// 存储用户会话信息
const sessions = new Map();

// 存储二维码登录状态
const qrLoginStatus = new Map();

// 代理登录页面请求
app.get('/api/proxy/login', async (req, res) => {
    try {
        console.log('正在获取登录页面...');

        // 创建axios实例以保持会话
        const axiosInstance = axios.create({
            timeout: config.request.timeout,
            headers: {
                'User-Agent': config.request.userAgent,
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            }
        });

        const response = await axiosInstance.get(config.target.loginUrl);
        const html = response.data;
        const cookies = response.headers['set-cookie'] || [];

        console.log('登录页面获取成功，页面长度:', html.length);

        // 使用多种方法查找二维码
        let qrCode = null;
        let qrCodeId = null;

        // 方法1: 查找base64编码的二维码
        const base64Match = html.match(/data:image\/png;base64,[A-Za-z0-9+/=]+/);
        if (base64Match) {
            qrCode = base64Match[0];
            console.log('找到base64二维码');
        }

        // 方法2: 使用cheerio解析HTML查找二维码相关元素
        if (!qrCode) {
            const $ = cheerio.load(html);

            // 查找包含二维码的img标签
            $('img').each((i, elem) => {
                const src = $(elem).attr('src');
                const alt = $(elem).attr('alt');
                const className = $(elem).attr('class');

                if (src && (
                    src.includes('qr') ||
                    src.includes('code') ||
                    src.startsWith('data:image') ||
                    (alt && alt.includes('二维码')) ||
                    (className && className.includes('qr'))
                )) {
                    qrCode = src;
                    console.log('通过img标签找到二维码:', src.substring(0, 50));
                    return false; // 跳出循环
                }
            });
        }

        // 方法3: 查找可能的二维码API接口
        if (!qrCode) {
            const scriptMatches = html.match(/\/api\/[^"']*qr[^"']*/gi);
            if (scriptMatches && scriptMatches.length > 0) {
                const qrApiUrl = scriptMatches[0];
                console.log('找到二维码API:', qrApiUrl);

                try {
                    const qrResponse = await axiosInstance.get(`https://www.goofish.pro${qrApiUrl}`);
                    if (qrResponse.data && qrResponse.data.qrCode) {
                        qrCode = qrResponse.data.qrCode;
                        qrCodeId = qrResponse.data.qrCodeId || qrResponse.data.id;
                    }
                } catch (qrError) {
                    console.log('二维码API调用失败:', qrError.message);
                }
            }
        }

        if (qrCode) {
            // 生成会话ID并存储相关信息
            const sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

            sessions.set(sessionId, {
                cookies: cookies,
                qrCodeId: qrCodeId,
                loginTime: null,
                loggedIn: false,
                axiosInstance: axiosInstance
            });

            res.json({
                success: true,
                qrCode: qrCode,
                sessionId: sessionId,
                message: '二维码获取成功'
            });
        } else {
            console.log('未找到二维码，返回HTML片段用于调试');
            res.json({
                success: false,
                message: '未找到二维码',
                htmlSnippet: html.substring(0, 2000),
                debug: {
                    htmlLength: html.length,
                    containsQr: html.toLowerCase().includes('qr'),
                    containsCode: html.toLowerCase().includes('code'),
                    containsLogin: html.toLowerCase().includes('login')
                }
            });
        }

    } catch (error) {
        console.error('获取登录页面失败:', error.message);
        res.status(500).json({
            success: false,
            message: `获取登录页面失败: ${error.message}`,
            error: error.response ? error.response.status : 'Network Error'
        });
    }
});

// 检查登录状态
app.post('/api/check-login', async (req, res) => {
    try {
        const { sessionId } = req.body;
        const session = sessions.get(sessionId);

        if (!session) {
            return res.json({
                success: false,
                isLoggedIn: false,
                message: '会话不存在，请重新获取二维码'
            });
        }

        // 如果已经登录，直接返回
        if (session.loggedIn) {
            return res.json({
                success: true,
                isLoggedIn: true,
                message: '已登录'
            });
        }

        try {
            // 检查登录状态的几种方法

            // 方法1: 尝试访问需要登录的页面
            const testResponse = await session.axiosInstance.get('https://www.goofish.pro/dashboard', {
                headers: {
                    'Cookie': session.cookies.join('; ')
                }
            });

            // 如果能成功访问dashboard，说明已登录
            if (testResponse.status === 200 && !testResponse.data.includes('login')) {
                session.loggedIn = true;
                session.loginTime = new Date();

                // 更新cookies
                if (testResponse.headers['set-cookie']) {
                    session.cookies = [...session.cookies, ...testResponse.headers['set-cookie']];
                }

                console.log('登录状态检查：已登录');
                return res.json({
                    success: true,
                    isLoggedIn: true,
                    message: '登录成功'
                });
            }

            // 方法2: 检查二维码状态API
            if (session.qrCodeId) {
                try {
                    const qrStatusResponse = await session.axiosInstance.get(`https://api.goofish.pro/api/qr/status/${session.qrCodeId}`, {
                        headers: {
                            'Cookie': session.cookies.join('; ')
                        }
                    });

                    if (qrStatusResponse.data && qrStatusResponse.data.status === 'success') {
                        session.loggedIn = true;
                        session.loginTime = new Date();

                        if (qrStatusResponse.headers['set-cookie']) {
                            session.cookies = [...session.cookies, ...qrStatusResponse.headers['set-cookie']];
                        }

                        console.log('二维码状态检查：登录成功');
                        return res.json({
                            success: true,
                            isLoggedIn: true,
                            message: '登录成功'
                        });
                    }
                } catch (qrError) {
                    console.log('二维码状态检查失败:', qrError.message);
                }
            }

            // 方法3: 尝试调用需要认证的API
            try {
                const apiTestResponse = await session.axiosInstance.get('https://api.goofish.pro/api/user/info', {
                    headers: {
                        'Cookie': session.cookies.join('; '),
                        'Accept': 'application/json'
                    }
                });

                if (apiTestResponse.status === 200 && apiTestResponse.data && !apiTestResponse.data.error) {
                    session.loggedIn = true;
                    session.loginTime = new Date();

                    if (apiTestResponse.headers['set-cookie']) {
                        session.cookies = [...session.cookies, ...apiTestResponse.headers['set-cookie']];
                    }

                    console.log('API测试：登录成功');
                    return res.json({
                        success: true,
                        isLoggedIn: true,
                        message: '登录成功'
                    });
                }
            } catch (apiError) {
                console.log('API测试失败:', apiError.message);
            }

        } catch (checkError) {
            console.log('登录状态检查出错:', checkError.message);
        }

        // 如果所有检查都失败，返回未登录状态
        res.json({
            success: true,
            isLoggedIn: false,
            message: '等待登录'
        });

    } catch (error) {
        console.error('检查登录状态失败:', error.message);
        res.status(500).json({
            success: false,
            message: `检查登录状态失败: ${error.message}`
        });
    }
});

// 代理收益数据请求
app.get('/api/proxy/earnings', async (req, res) => {
    try {
        const { sessionId } = req.query;
        const session = sessions.get(sessionId);

        if (!session) {
            return res.status(401).json({
                success: false,
                message: '会话不存在，请重新登录'
            });
        }

        if (!session.loggedIn) {
            return res.status(401).json({
                success: false,
                message: '请先登录'
            });
        }

        console.log('正在获取收益数据...');

        // 构建完整的API URL
        const apiUrl = 'https://api.goofish.pro/api/stats/order';
        const params = {
            channel: 1,
            version: '3.54.31',
            authorize_id: 0,
            date_type: 1,
            selectIndex: 0
        };

        try {
            // 使用会话中的axios实例和cookies
            const response = await session.axiosInstance.get(apiUrl, {
                params: params,
                headers: {
                    'User-Agent': config.request.userAgent,
                    'Accept': 'application/json, text/plain, */*',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'Cookie': session.cookies.join('; '),
                    'Referer': 'https://www.goofish.pro/',
                    'Origin': 'https://www.goofish.pro',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            console.log('API响应状态:', response.status);
            console.log('API响应数据:', JSON.stringify(response.data, null, 2));

            // 检查响应数据格式
            if (response.data) {
                let targetData = null;

                // 尝试不同的数据结构
                if (response.data.data && Array.isArray(response.data.data) && response.data.data.length > 0) {
                    targetData = response.data.data[0];
                } else if (Array.isArray(response.data) && response.data.length > 0) {
                    targetData = response.data[0];
                } else if (response.data.result && Array.isArray(response.data.result) && response.data.result.length > 0) {
                    targetData = response.data.result[0];
                }

                if (targetData && (targetData.num !== undefined || targetData.account !== undefined)) {
                    const result = {
                        num: targetData.num || targetData.orderCount || targetData.total_orders || 0,
                        account: targetData.account || targetData.totalAmount || targetData.total_amount || targetData.earnings || 0
                    };

                    console.log('成功提取数据:', result);

                    res.json({
                        success: true,
                        data: result,
                        message: '数据获取成功',
                        rawData: response.data // 用于调试
                    });
                    return;
                }
            }

            // 如果数据格式不符合预期，返回原始数据用于调试
            console.log('数据格式不符合预期，返回原始数据');
            res.json({
                success: false,
                message: '数据格式不符合预期',
                rawData: response.data,
                expectedFormat: '期望数据格式: {data: [{num: number, account: number}]}'
            });

        } catch (apiError) {
            console.error('API调用失败:', apiError.message);
            console.error('错误详情:', apiError.response ? {
                status: apiError.response.status,
                statusText: apiError.response.statusText,
                data: apiError.response.data
            } : '网络错误');

            // 如果是认证错误，尝试重新登录
            if (apiError.response && (apiError.response.status === 401 || apiError.response.status === 403)) {
                session.loggedIn = false;
                return res.status(401).json({
                    success: false,
                    message: '登录已过期，请重新登录',
                    needRelogin: true
                });
            }

            res.status(500).json({
                success: false,
                message: `API调用失败: ${apiError.message}`,
                error: apiError.response ? {
                    status: apiError.response.status,
                    data: apiError.response.data
                } : 'Network Error'
            });
        }

    } catch (error) {
        console.error('获取收益数据失败:', error.message);
        res.status(500).json({
            success: false,
            message: `获取收益数据失败: ${error.message}`
        });
    }
});

// 生成会话ID
app.get('/api/session', (req, res) => {
    const sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    res.json({
        success: true,
        sessionId: sessionId
    });
});

// 健康检查
app.get('/api/health', (req, res) => {
    res.json({
        success: true,
        message: '服务器运行正常',
        timestamp: new Date().toISOString()
    });
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`代理服务器运行在 http://localhost:${PORT}`);
    console.log('请在浏览器中访问 http://localhost:3000 来使用闲鱼收益监控器');
});

module.exports = app;
