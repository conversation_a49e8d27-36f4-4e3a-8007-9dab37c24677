<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>闲鱼收益监控器</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .content {
            padding: 30px;
        }
        
        .login-section {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .qr-container {
            display: inline-block;
            padding: 20px;
            border: 2px dashed #ddd;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .qr-code {
            max-width: 200px;
            max-height: 200px;
        }
        
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
        }
        
        .status.info {
            background: #e3f2fd;
            color: #1976d2;
            border-left: 4px solid #2196f3;
        }
        
        .status.success {
            background: #e8f5e8;
            color: #2e7d32;
            border-left: 4px solid #4caf50;
        }
        
        .status.error {
            background: #ffebee;
            color: #c62828;
            border-left: 4px solid #f44336;
        }
        
        .data-section {
            display: none;
            margin-top: 30px;
        }
        
        .data-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .data-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .data-card h3 {
            margin: 0 0 10px 0;
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .data-card .value {
            font-size: 2.5em;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .btn {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛒 闲鱼收益监控器</h1>
            <p>实时监控您的闲鱼收益数据</p>
        </div>
        
        <div class="content">
            <div class="login-section">
                <h2>登录闲管家</h2>
                <button id="getQrBtn" class="btn">获取登录二维码</button>
                
                <div id="qrContainer" class="qr-container" style="display: none;">
                    <img id="qrCode" class="qr-code" alt="登录二维码">
                    <p>请使用闲鱼APP扫描二维码登录</p>
                </div>
                
                <div id="status" class="status info" style="display: none;">
                    等待获取二维码...
                </div>
            </div>
            
            <div id="dataSection" class="data-section">
                <h2>📊 收益数据</h2>
                <button id="refreshBtn" class="btn">刷新数据</button>
                
                <div class="data-cards">
                    <div class="data-card">
                        <h3>📦 订单总数</h3>
                        <div id="orderCount" class="value">--</div>
                    </div>
                    <div class="data-card">
                        <h3>💰 总收益</h3>
                        <div id="totalEarnings" class="value">--</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
