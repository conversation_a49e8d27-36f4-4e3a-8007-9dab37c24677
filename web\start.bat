@echo off
echo ================================
echo 闲鱼收益监控器启动脚本
echo ================================
echo.

:: 检查Node.js是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未检测到Node.js，请先安装Node.js
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

echo 检测到Node.js版本:
node --version
echo.

:: 检查是否存在node_modules目录
if not exist "node_modules" (
    echo 正在安装依赖包...
    npm install
    if %errorlevel% neq 0 (
        echo 错误: 依赖包安装失败
        pause
        exit /b 1
    )
    echo 依赖包安装完成!
    echo.
) else (
    echo 依赖包已存在，跳过安装
    echo.
)

:: 启动服务器
echo 正在启动闲鱼收益监控器...
echo 服务器地址: http://localhost:3000
echo.
echo 按 Ctrl+C 停止服务器
echo ================================
echo.

node proxy-server.js

pause
