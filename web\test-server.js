const http = require('http');
const https = require('https');
const url = require('url');

const PORT = 3000;

console.log('正在启动测试服务器...');

// 发送HTTPS请求的辅助函数
function makeHttpsRequest(options) {
    return new Promise((resolve, reject) => {
        const req = https.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                resolve({
                    statusCode: res.statusCode,
                    headers: res.headers,
                    data: data
                });
            });
        });
        
        req.on('error', (error) => {
            reject(error);
        });
        
        req.setTimeout(10000, () => {
            req.destroy();
            reject(new Error('请求超时'));
        });
        
        req.end();
    });
}

// 测试获取登录页面
async function testLoginPage() {
    console.log('\n=== 测试获取登录页面 ===');

    const options = {
        hostname: 'www.goofish.pro',
        port: 443,
        path: '/login',
        method: 'GET',
        headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
        }
    };

    try {
        const response = await makeHttpsRequest(options);
        console.log('状态码:', response.statusCode);
        console.log('响应长度:', response.data.length);

        // 这是一个SPA应用，二维码通过JavaScript动态生成
        console.log('📝 检测到SPA应用，需要执行JavaScript获取二维码');

        // 查找JavaScript文件
        const jsMatches = response.data.match(/<script[^>]+src="([^"]+)"[^>]*>/gi);
        if (jsMatches) {
            console.log('找到JavaScript文件数量:', jsMatches.length);

            // 尝试获取主要的应用JS文件
            const appJsMatch = response.data.match(/src="(\/app_[^"]+\.js)"/);
            if (appJsMatch) {
                console.log('主应用JS文件:', appJsMatch[1]);
                await analyzeAppJs(appJsMatch[1]);
            }
        }

        // 保存HTML到文件用于调试
        require('fs').writeFileSync('debug-login.html', response.data);
        console.log('HTML已保存到 debug-login.html');

        return null;

    } catch (error) {
        console.error('❌ 获取登录页面失败:', error.message);
        return null;
    }
}

// 分析应用JavaScript文件
async function analyzeAppJs(jsPath) {
    console.log('\n=== 分析应用JavaScript ===');

    const options = {
        hostname: 'www.goofish.pro',
        port: 443,
        path: jsPath,
        method: 'GET',
        headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/javascript, */*',
            'Referer': 'https://www.goofish.pro/login'
        }
    };

    try {
        const response = await makeHttpsRequest(options);
        console.log('JS文件状态码:', response.statusCode);
        console.log('JS文件大小:', response.data.length);

        // 查找二维码相关的API调用
        const qrApiMatches = response.data.match(/['"](\/api\/[^'"]*qr[^'"]*)['"]/gi);
        if (qrApiMatches) {
            console.log('找到二维码API:', qrApiMatches);
        }

        // 查找登录相关的API
        const loginApiMatches = response.data.match(/['"](\/api\/[^'"]*login[^'"]*)['"]/gi);
        if (loginApiMatches) {
            console.log('找到登录API:', loginApiMatches);
        }

        // 查找所有API端点
        const allApiMatches = response.data.match(/['"](\/api\/[^'"]+)['"]/gi);
        if (allApiMatches) {
            const uniqueApis = [...new Set(allApiMatches)];
            console.log('所有API端点:', uniqueApis.slice(0, 10)); // 只显示前10个
        }

        // 保存JS文件用于进一步分析
        require('fs').writeFileSync('debug-app.js', response.data);
        console.log('JS文件已保存到 debug-app.js');

    } catch (error) {
        console.error('❌ 分析JS文件失败:', error.message);
    }
}

// 测试二维码API
async function testQRCodeAPI() {
    console.log('\n=== 测试二维码API ===');

    const options = {
        hostname: 'www.goofish.pro',
        port: 443,
        path: '/api/user/login/dingQrcode',
        method: 'GET',
        headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Referer': 'https://www.goofish.pro/login',
            'Origin': 'https://www.goofish.pro'
        }
    };

    try {
        const response = await makeHttpsRequest(options);
        console.log('二维码API状态码:', response.statusCode);
        console.log('二维码API响应长度:', response.data.length);
        console.log('二维码API响应:', response.data.substring(0, 500));

        if (response.statusCode === 200) {
            try {
                const data = JSON.parse(response.data);
                console.log('✅ 二维码API响应解析成功');
                console.log('数据结构:', Object.keys(data));

                // 检查是否包含二维码数据
                if (data.data && typeof data.data === 'string' && data.data.startsWith('data:image')) {
                    console.log('🎉 找到二维码数据！');
                    return data;
                } else if (data.data && data.data.qrCode) {
                    console.log('🎉 找到二维码字段！');
                    return data;
                } else {
                    console.log('📋 响应数据:', JSON.stringify(data, null, 2));
                }

                return data;
            } catch (parseError) {
                console.log('❌ JSON解析失败:', parseError.message);
                console.log('原始响应:', response.data);
            }
        }

    } catch (error) {
        console.error('❌ 二维码API调用失败:', error.message);
    }

    return null;
}

// 测试API接口
async function testApiEndpoint() {
    console.log('\n=== 测试API接口 ===');

    const options = {
        hostname: 'api.goofish.pro',
        port: 443,
        path: '/api/stats/order?channel=1&version=3.54.31&authorize_id=0&date_type=1&selectIndex=0',
        method: 'GET',
        headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
        }
    };

    try {
        const response = await makeHttpsRequest(options);
        console.log('API状态码:', response.statusCode);
        console.log('API响应:', response.data.substring(0, 500));

        if (response.statusCode === 200) {
            try {
                const data = JSON.parse(response.data);
                console.log('✅ API响应解析成功');
                console.log('数据结构:', Object.keys(data));
                return data;
            } catch (parseError) {
                console.log('❌ JSON解析失败:', parseError.message);
            }
        }

    } catch (error) {
        console.error('❌ API调用失败:', error.message);
    }

    return null;
}

// 创建HTTP服务器
const server = http.createServer(async (req, res) => {
    const parsedUrl = url.parse(req.url, true);
    const pathname = parsedUrl.pathname;
    
    // 设置CORS头
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
    
    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }
    
    console.log(`${req.method} ${pathname}`);
    
    if (pathname === '/test-login') {
        const qrCode = await testLoginPage();
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
            success: !!qrCode,
            qrCode: qrCode,
            message: qrCode ? '二维码获取成功' : '未找到二维码'
        }));
        
    } else if (pathname === '/test-api') {
        const apiData = await testApiEndpoint();
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
            success: !!apiData,
            data: apiData,
            message: apiData ? 'API测试成功' : 'API测试失败'
        }));

    } else if (pathname === '/test-qrcode') {
        const qrData = await testQRCodeAPI();
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
            success: !!qrData,
            data: qrData,
            message: qrData ? '二维码API测试成功' : '二维码API测试失败'
        }));
        
    } else if (pathname === '/') {
        // 返回简单的测试页面
        res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
        res.end(`
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>闲鱼API测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        button { padding: 10px 20px; margin: 10px; font-size: 16px; }
        .result { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
        pre { background: #f5f5f5; padding: 10px; overflow: auto; }
    </style>
</head>
<body>
    <h1>闲鱼API测试工具</h1>
    
    <button onclick="testLogin()">测试登录页面</button>
    <button onclick="testQRCode()">测试二维码API</button>
    <button onclick="testApi()">测试API接口</button>
    
    <div id="result" class="result" style="display:none;">
        <h3>测试结果:</h3>
        <pre id="output"></pre>
    </div>
    
    <script>
        async function testLogin() {
            showResult('正在测试登录页面...');
            try {
                const response = await fetch('/test-login');
                const data = await response.json();
                showResult(JSON.stringify(data, null, 2));
            } catch (error) {
                showResult('错误: ' + error.message);
            }
        }
        
        async function testQRCode() {
            showResult('正在测试二维码API...');
            try {
                const response = await fetch('/test-qrcode');
                const data = await response.json();
                showResult(JSON.stringify(data, null, 2));
            } catch (error) {
                showResult('错误: ' + error.message);
            }
        }

        async function testApi() {
            showResult('正在测试API接口...');
            try {
                const response = await fetch('/test-api');
                const data = await response.json();
                showResult(JSON.stringify(data, null, 2));
            } catch (error) {
                showResult('错误: ' + error.message);
            }
        }
        
        function showResult(text) {
            document.getElementById('result').style.display = 'block';
            document.getElementById('output').textContent = text;
        }
    </script>
</body>
</html>
        `);
        
    } else {
        res.writeHead(404, { 'Content-Type': 'text/plain' });
        res.end('Not Found');
    }
});

// 启动服务器
server.listen(PORT, () => {
    console.log(`\n🚀 测试服务器启动成功!`);
    console.log(`📱 访问地址: http://localhost:${PORT}`);
    console.log(`🔧 测试登录: http://localhost:${PORT}/test-login`);
    console.log(`🔧 测试API: http://localhost:${PORT}/test-api`);
    console.log(`\n按 Ctrl+C 停止服务器\n`);
});

// 启动时自动测试
setTimeout(async () => {
    console.log('开始自动测试...');
    await testLoginPage();
    await testQRCodeAPI();
    await testApiEndpoint();
}, 1000);
