const express = require('express');
const cors = require('cors');
const axios = require('axios');
const path = require('path');
const config = require('./config');
const puppeteer = require('puppeteer');

const app = express();
const PORT = config.server.port;

// 启用CORS
app.use(cors());
app.use(express.json());

// 提供静态文件服务
app.use(express.static(path.join(__dirname)));

// 存储用户会话信息
const sessions = new Map();

// 存储浏览器实例
let browser = null;

// 初始化浏览器
async function initBrowser() {
    if (!browser) {
        try {
            browser = await puppeteer.launch({
                headless: true,
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu'
                ]
            });
            console.log('浏览器初始化成功');
        } catch (error) {
            console.error('浏览器初始化失败:', error.message);
            throw error;
        }
    }
    return browser;
}

// 使用Puppeteer获取登录页面和二维码
app.get('/api/proxy/login', async (req, res) => {
    let page = null;
    try {
        console.log('正在使用Puppeteer获取登录页面...');
        
        const browserInstance = await initBrowser();
        page = await browserInstance.newPage();
        
        // 设置用户代理
        await page.setUserAgent(config.request.userAgent);
        
        // 设置视口
        await page.setViewport({ width: 1280, height: 720 });
        
        // 拦截网络请求以获取cookies
        const cookies = [];
        await page.setRequestInterception(true);
        page.on('request', (request) => {
            request.continue();
        });
        
        page.on('response', (response) => {
            const responseHeaders = response.headers();
            if (responseHeaders['set-cookie']) {
                cookies.push(...responseHeaders['set-cookie']);
            }
        });
        
        // 导航到登录页面
        console.log('正在访问登录页面...');
        await page.goto(config.target.loginUrl, { 
            waitUntil: 'networkidle2',
            timeout: 30000 
        });
        
        // 等待页面加载完成
        await page.waitForTimeout(3000);
        
        // 查找二维码的多种方法
        let qrCodeData = null;
        let qrCodeId = null;
        
        // 方法1: 查找包含base64的img元素
        console.log('方法1: 查找base64二维码...');
        const base64QR = await page.evaluate(() => {
            const images = document.querySelectorAll('img');
            for (let img of images) {
                if (img.src && img.src.startsWith('data:image/png;base64')) {
                    return img.src;
                }
            }
            return null;
        });
        
        if (base64QR) {
            qrCodeData = base64QR;
            console.log('找到base64二维码');
        }
        
        // 方法2: 查找二维码相关的元素并截图
        if (!qrCodeData) {
            console.log('方法2: 查找二维码元素并截图...');
            const qrElement = await page.$('.qr-code, .qrcode, [class*="qr"], [id*="qr"], [class*="code"]');
            if (qrElement) {
                const screenshot = await qrElement.screenshot({ encoding: 'base64' });
                qrCodeData = `data:image/png;base64,${screenshot}`;
                console.log('通过截图获取二维码');
            }
        }
        
        // 方法3: 监听网络请求中的二维码API
        if (!qrCodeData) {
            console.log('方法3: 监听二维码API请求...');
            
            // 等待可能的异步二维码加载
            await page.waitForTimeout(5000);
            
            // 再次检查是否有二维码出现
            const delayedQR = await page.evaluate(() => {
                const images = document.querySelectorAll('img');
                for (let img of images) {
                    if (img.src && (
                        img.src.startsWith('data:image/png;base64') ||
                        img.src.includes('qr') ||
                        img.src.includes('code')
                    )) {
                        return img.src;
                    }
                }
                return null;
            });
            
            if (delayedQR) {
                qrCodeData = delayedQR;
                console.log('延迟加载后找到二维码');
            }
        }
        
        // 方法4: 查找可能的二维码容器并截图
        if (!qrCodeData) {
            console.log('方法4: 截图二维码容器...');
            const qrContainer = await page.$('.login-qr, .qr-container, .qr-wrapper, [class*="login"]');
            if (qrContainer) {
                const screenshot = await qrContainer.screenshot({ encoding: 'base64' });
                qrCodeData = `data:image/png;base64,${screenshot}`;
                console.log('通过容器截图获取二维码');
            }
        }
        
        // 获取页面cookies
        const pageCookies = await page.cookies();
        const cookieStrings = pageCookies.map(cookie => `${cookie.name}=${cookie.value}`);
        
        if (qrCodeData) {
            // 生成会话ID并存储相关信息
            const sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            
            sessions.set(sessionId, {
                cookies: [...cookies, ...cookieStrings],
                qrCodeId: qrCodeId,
                loginTime: null,
                loggedIn: false,
                page: page, // 保持页面实例用于后续操作
                browserInstance: browserInstance
            });
            
            res.json({
                success: true,
                qrCode: qrCodeData,
                sessionId: sessionId,
                message: '二维码获取成功'
            });
        } else {
            // 获取页面内容用于调试
            const pageContent = await page.content();
            const pageTitle = await page.title();
            
            res.json({
                success: false,
                message: '未找到二维码',
                debug: {
                    pageTitle: pageTitle,
                    pageLength: pageContent.length,
                    url: page.url(),
                    cookies: cookieStrings.length
                }
            });
            
            // 关闭页面
            await page.close();
        }

    } catch (error) {
        console.error('获取登录页面失败:', error.message);
        
        if (page) {
            try {
                await page.close();
            } catch (closeError) {
                console.error('关闭页面失败:', closeError.message);
            }
        }
        
        res.status(500).json({
            success: false,
            message: `获取登录页面失败: ${error.message}`,
            error: error.name
        });
    }
});

// 检查登录状态
app.post('/api/check-login', async (req, res) => {
    try {
        const { sessionId } = req.body;
        const session = sessions.get(sessionId);
        
        if (!session) {
            return res.json({
                success: false,
                isLoggedIn: false,
                message: '会话不存在，请重新获取二维码'
            });
        }
        
        // 如果已经登录，直接返回
        if (session.loggedIn) {
            return res.json({
                success: true,
                isLoggedIn: true,
                message: '已登录'
            });
        }
        
        try {
            // 使用保存的页面实例检查登录状态
            if (session.page && !session.page.isClosed()) {
                // 检查页面URL是否已经跳转到登录后的页面
                const currentUrl = session.page.url();
                console.log('当前页面URL:', currentUrl);
                
                // 如果URL包含dashboard或其他登录后的标识，说明已登录
                if (currentUrl.includes('dashboard') || 
                    currentUrl.includes('home') || 
                    currentUrl.includes('main') ||
                    !currentUrl.includes('login')) {
                    
                    session.loggedIn = true;
                    session.loginTime = new Date();
                    
                    // 更新cookies
                    const pageCookies = await session.page.cookies();
                    const cookieStrings = pageCookies.map(cookie => `${cookie.name}=${cookie.value}`);
                    session.cookies = [...session.cookies, ...cookieStrings];
                    
                    console.log('登录状态检查：已登录');
                    return res.json({
                        success: true,
                        isLoggedIn: true,
                        message: '登录成功'
                    });
                }
                
                // 检查页面内容是否包含登录成功的标识
                const pageContent = await session.page.content();
                if (pageContent.includes('欢迎') || 
                    pageContent.includes('dashboard') || 
                    pageContent.includes('工作台')) {
                    
                    session.loggedIn = true;
                    session.loginTime = new Date();
                    
                    console.log('页面内容检查：已登录');
                    return res.json({
                        success: true,
                        isLoggedIn: true,
                        message: '登录成功'
                    });
                }
            }
            
        } catch (checkError) {
            console.log('登录状态检查出错:', checkError.message);
        }
        
        // 如果所有检查都失败，返回未登录状态
        res.json({
            success: true,
            isLoggedIn: false,
            message: '等待登录'
        });

    } catch (error) {
        console.error('检查登录状态失败:', error.message);
        res.status(500).json({
            success: false,
            message: `检查登录状态失败: ${error.message}`
        });
    }
});

// 代理收益数据请求
app.get('/api/proxy/earnings', async (req, res) => {
    try {
        const { sessionId } = req.query;
        const session = sessions.get(sessionId);
        
        if (!session) {
            return res.status(401).json({
                success: false,
                message: '会话不存在，请重新登录'
            });
        }
        
        if (!session.loggedIn) {
            return res.status(401).json({
                success: false,
                message: '请先登录'
            });
        }

        console.log('正在获取收益数据...');
        
        // 构建完整的API URL
        const apiUrl = 'https://api.goofish.pro/api/stats/order';
        const params = new URLSearchParams({
            channel: '1',
            version: '3.54.31',
            authorize_id: '0',
            date_type: '1',
            selectIndex: '0'
        });

        try {
            // 使用axios调用API
            const response = await axios.get(`${apiUrl}?${params}`, {
                headers: {
                    'User-Agent': config.request.userAgent,
                    'Accept': 'application/json, text/plain, */*',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'Cookie': session.cookies.join('; '),
                    'Referer': 'https://www.goofish.pro/',
                    'Origin': 'https://www.goofish.pro',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                timeout: config.request.timeout
            });

            console.log('API响应状态:', response.status);
            console.log('API响应数据:', JSON.stringify(response.data, null, 2));

            // 检查响应数据格式
            if (response.data) {
                let targetData = null;
                
                // 尝试不同的数据结构
                if (response.data.data && Array.isArray(response.data.data) && response.data.data.length > 0) {
                    targetData = response.data.data[0];
                } else if (Array.isArray(response.data) && response.data.length > 0) {
                    targetData = response.data[0];
                } else if (response.data.result && Array.isArray(response.data.result) && response.data.result.length > 0) {
                    targetData = response.data.result[0];
                }
                
                if (targetData && (targetData.num !== undefined || targetData.account !== undefined)) {
                    const result = {
                        num: targetData.num || targetData.orderCount || targetData.total_orders || 0,
                        account: targetData.account || targetData.totalAmount || targetData.total_amount || targetData.earnings || 0
                    };
                    
                    console.log('成功提取数据:', result);
                    
                    res.json({
                        success: true,
                        data: result,
                        message: '数据获取成功',
                        rawData: response.data
                    });
                    return;
                }
            }
            
            // 如果数据格式不符合预期，返回原始数据用于调试
            console.log('数据格式不符合预期，返回原始数据');
            res.json({
                success: false,
                message: '数据格式不符合预期',
                rawData: response.data,
                expectedFormat: '期望数据格式: {data: [{num: number, account: number}]}'
            });

        } catch (apiError) {
            console.error('API调用失败:', apiError.message);
            
            // 如果是认证错误，尝试重新登录
            if (apiError.response && (apiError.response.status === 401 || apiError.response.status === 403)) {
                session.loggedIn = false;
                return res.status(401).json({
                    success: false,
                    message: '登录已过期，请重新登录',
                    needRelogin: true
                });
            }
            
            res.status(500).json({
                success: false,
                message: `API调用失败: ${apiError.message}`,
                error: apiError.response ? {
                    status: apiError.response.status,
                    data: apiError.response.data
                } : 'Network Error'
            });
        }

    } catch (error) {
        console.error('获取收益数据失败:', error.message);
        res.status(500).json({
            success: false,
            message: `获取收益数据失败: ${error.message}`
        });
    }
});

// 生成会话ID
app.get('/api/session', (req, res) => {
    const sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    res.json({
        success: true,
        sessionId: sessionId
    });
});

// 健康检查
app.get('/api/health', (req, res) => {
    res.json({
        success: true,
        message: '服务器运行正常',
        timestamp: new Date().toISOString(),
        browser: browser ? 'initialized' : 'not initialized'
    });
});

// 清理资源
process.on('SIGINT', async () => {
    console.log('正在清理资源...');
    
    // 关闭所有会话的页面
    for (const [sessionId, session] of sessions) {
        if (session.page && !session.page.isClosed()) {
            try {
                await session.page.close();
            } catch (error) {
                console.error(`关闭会话 ${sessionId} 的页面失败:`, error.message);
            }
        }
    }
    
    // 关闭浏览器
    if (browser) {
        try {
            await browser.close();
        } catch (error) {
            console.error('关闭浏览器失败:', error.message);
        }
    }
    
    process.exit(0);
});

// 启动服务器
app.listen(PORT, async () => {
    console.log(`代理服务器运行在 http://localhost:${PORT}`);
    console.log(`请在浏览器中访问 http://localhost:${PORT} 来使用闲鱼收益监控器`);
    
    // 预初始化浏览器
    try {
        await initBrowser();
        console.log('浏览器预初始化完成');
    } catch (error) {
        console.error('浏览器预初始化失败:', error.message);
    }
});

module.exports = app;
