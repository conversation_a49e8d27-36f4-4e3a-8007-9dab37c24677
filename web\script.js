class XianYuMonitor {
    constructor() {
        this.isLoggedIn = false;
        this.sessionId = null;
        this.checkInterval = null;
        this.baseUrl = window.location.origin;
        this.init();
    }

    async init() {
        document.getElementById('getQrBtn').addEventListener('click', () => this.getQRCode());
        document.getElementById('refreshBtn').addEventListener('click', () => this.fetchEarningsData());

        // 获取会话ID
        await this.getSessionId();
    }

    async getSessionId() {
        try {
            const response = await fetch(`${this.baseUrl}/api/session`);
            const data = await response.json();
            if (data.success) {
                this.sessionId = data.sessionId;
                console.log('会话ID获取成功:', this.sessionId);
            }
        } catch (error) {
            console.error('获取会话ID失败:', error);
        }
    }

    showStatus(message, type = 'info') {
        const statusEl = document.getElementById('status');
        statusEl.textContent = message;
        statusEl.className = `status ${type}`;
        statusEl.style.display = 'block';
    }

    showLoading(elementId, show = true) {
        const element = document.getElementById(elementId);
        if (show) {
            element.innerHTML = '<span class="loading"></span>加载中...';
            element.disabled = true;
        } else {
            element.innerHTML = element.dataset.originalText || element.textContent.replace('加载中...', '');
            element.disabled = false;
        }
    }

    async getQRCode() {
        try {
            this.showLoading('getQrBtn');
            this.showStatus('正在获取登录二维码...', 'info');

            const response = await fetch(`${this.baseUrl}/api/proxy/login`);
            const data = await response.json();

            if (data.success && data.qrCode) {
                // 更新会话ID
                if (data.sessionId) {
                    this.sessionId = data.sessionId;
                }

                this.displayQRCode(data.qrCode);
                this.startLoginCheck();
                this.showStatus('二维码获取成功，请使用闲鱼APP扫描登录', 'success');
            } else {
                this.showStatus(data.message || '获取二维码失败', 'error');

                // 如果有调试信息，显示给用户
                if (data.debug) {
                    console.log('调试信息:', data.debug);
                    this.showStatus(`获取二维码失败: ${data.message}。页面长度: ${data.debug.htmlLength}`, 'error');
                }

                // 显示备用二维码
                await this.getQRCodeAlternative();
            }

        } catch (error) {
            console.error('获取二维码失败:', error);
            this.showStatus(`网络错误: ${error.message}`, 'error');
            await this.getQRCodeAlternative();
        } finally {
            this.showLoading('getQrBtn', false);
        }
    }

    async getQRCodeAlternative() {
        try {
            // 备用方案：模拟生成二维码（实际项目中需要真实的API）
            this.showStatus('正在生成模拟二维码...', 'info');
            
            // 这里应该调用真实的API获取二维码
            // 暂时使用占位符图片
            const placeholderQR = this.generatePlaceholderQR();
            this.displayQRCode(placeholderQR);
            this.showStatus('请扫描二维码登录（模拟模式）', 'info');
            
            // 模拟登录成功（实际项目中需要真实的登录检查）
            setTimeout(() => {
                this.onLoginSuccess();
            }, 5000);

        } catch (error) {
            this.showStatus(`备用方案失败: ${error.message}`, 'error');
        }
    }

    generatePlaceholderQR() {
        // 生成一个简单的占位符二维码图片
        const canvas = document.createElement('canvas');
        canvas.width = 200;
        canvas.height = 200;
        const ctx = canvas.getContext('2d');
        
        // 绘制简单的二维码样式
        ctx.fillStyle = '#000';
        ctx.fillRect(0, 0, 200, 200);
        ctx.fillStyle = '#fff';
        
        // 绘制一些方块模拟二维码
        for (let i = 0; i < 20; i++) {
            for (let j = 0; j < 20; j++) {
                if (Math.random() > 0.5) {
                    ctx.fillRect(i * 10, j * 10, 10, 10);
                }
            }
        }
        
        return canvas.toDataURL();
    }

    displayQRCode(qrCodeUrl) {
        const qrContainer = document.getElementById('qrContainer');
        const qrCode = document.getElementById('qrCode');
        
        qrCode.src = qrCodeUrl;
        qrContainer.style.display = 'block';
    }

    startLoginCheck() {
        // 定期检查登录状态
        this.checkInterval = setInterval(async () => {
            try {
                await this.checkLoginStatus();
            } catch (error) {
                console.error('检查登录状态失败:', error);
            }
        }, 3000);
    }

    async checkLoginStatus() {
        try {
            if (!this.sessionId) return;

            const response = await fetch(`${this.baseUrl}/api/check-login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    sessionId: this.sessionId
                })
            });

            const data = await response.json();

            if (data.success && data.isLoggedIn && !this.isLoggedIn) {
                this.onLoginSuccess();
            }
        } catch (error) {
            console.error('登录状态检查失败:', error);
        }
    }

    onLoginSuccess() {
        this.isLoggedIn = true;
        clearInterval(this.checkInterval);
        
        this.showStatus('登录成功！正在获取收益数据...', 'success');
        
        // 隐藏二维码，显示数据区域
        document.getElementById('qrContainer').style.display = 'none';
        document.getElementById('dataSection').style.display = 'block';
        
        // 获取收益数据
        this.fetchEarningsData();
    }

    async fetchEarningsData() {
        try {
            this.showLoading('refreshBtn');
            this.showStatus('正在获取收益数据...', 'info');

            if (!this.sessionId) {
                throw new Error('会话ID不存在，请重新登录');
            }

            const response = await fetch(`${this.baseUrl}/api/proxy/earnings?sessionId=${this.sessionId}`);
            const data = await response.json();

            if (data.success && data.data) {
                this.updateEarningsDisplay(data.data.num, data.data.account);
                this.showStatus(data.message || '数据更新成功', 'success');

                // 如果有原始数据，在控制台显示用于调试
                if (data.rawData) {
                    console.log('原始API数据:', data.rawData);
                }
            } else {
                // 检查是否需要重新登录
                if (data.needRelogin) {
                    this.isLoggedIn = false;
                    document.getElementById('dataSection').style.display = 'none';
                    document.getElementById('qrContainer').style.display = 'none';
                    this.showStatus('登录已过期，请重新获取二维码', 'error');
                    return;
                }

                // 显示详细错误信息
                let errorMsg = data.message || '数据获取失败';
                if (data.rawData) {
                    console.log('API返回的原始数据:', data.rawData);
                    errorMsg += '（请查看控制台了解详细信息）';
                }

                throw new Error(errorMsg);
            }

        } catch (error) {
            console.error('获取收益数据失败:', error);

            // 检查是否是网络错误
            if (error.message.includes('401') || error.message.includes('登录')) {
                this.isLoggedIn = false;
                document.getElementById('dataSection').style.display = 'none';
                this.showStatus('登录状态已失效，请重新登录', 'error');
            } else {
                this.showStatus(`获取数据失败: ${error.message}`, 'error');

                // 只有在非认证错误时才显示模拟数据
                if (!error.message.includes('登录') && !error.message.includes('401')) {
                    console.log('显示模拟数据用于演示');
                    this.showSimulatedData();
                }
            }
        } finally {
            this.showLoading('refreshBtn', false);
        }
    }

    showSimulatedData() {
        // 显示模拟数据用于演示
        const simulatedNum = Math.floor(Math.random() * 1000) + 100;
        const simulatedAccount = (Math.random() * 10000 + 1000).toFixed(2);
        
        this.updateEarningsDisplay(simulatedNum, simulatedAccount);
        this.showStatus('显示模拟数据（演示模式）', 'info');
    }

    updateEarningsDisplay(orderCount, totalEarnings) {
        document.getElementById('orderCount').textContent = orderCount;
        document.getElementById('totalEarnings').textContent = `¥${totalEarnings}`;
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new XianYuMonitor();
});
