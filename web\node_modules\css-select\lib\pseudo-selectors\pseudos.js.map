{"version": 3, "file": "pseudos.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/css-select/93caad96c807da1d48f08166ef14cf26916b9364/src/", "sources": ["pseudo-selectors/pseudos.ts"], "names": [], "mappings": ";;;AASA,yEAAyE;AAC5D,QAAA,OAAO,GAA2B;IAC3C,KAAK,YAAC,IAAI,EAAE,EAAW;YAAT,OAAO,aAAA;QACjB,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,IAAI,CAClC,UAAC,IAAI;YACD,kDAAkD;YAClD,OAAA,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE;QAAnD,CAAmD,CAC1D,CAAC;IACN,CAAC;IAED,aAAa,YAAC,IAAI,EAAE,EAAmB;YAAjB,OAAO,aAAA,EAAE,MAAM,YAAA;QACjC,IAAI,OAAO,CAAC,kBAAkB,EAAE;YAC5B,OAAO,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC;SACnD;QAED,IAAM,UAAU,GAAG,OAAO;aACrB,WAAW,CAAC,IAAI,CAAC;aACjB,IAAI,CAAC,UAAC,IAAI,IAAK,OAAA,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAnB,CAAmB,CAAC,CAAC;QACzC,OAAO,UAAU,IAAI,IAAI,IAAI,MAAM,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;IAC1D,CAAC;IACD,YAAY,YAAC,IAAI,EAAE,EAAmB;YAAjB,OAAO,aAAA,EAAE,MAAM,YAAA;QAChC,IAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAE3C,KAAK,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;YAC3C,IAAI,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAAE,OAAO,IAAI,CAAC;YAC3C,IAAI,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAAE,MAAM;SACzC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IACD,eAAe,YAAC,IAAI,EAAE,EAAmB;YAAjB,OAAO,aAAA,EAAE,MAAM,YAAA;QACnC,IAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAC3C,IAAM,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAEvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACtC,IAAM,cAAc,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YACnC,IAAI,MAAM,CAAC,IAAI,EAAE,cAAc,CAAC;gBAAE,OAAO,IAAI,CAAC;YAC9C,IACI,OAAO,CAAC,KAAK,CAAC,cAAc,CAAC;gBAC7B,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,QAAQ,EAC9C;gBACE,MAAM;aACT;SACJ;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IACD,cAAc,YAAC,IAAI,EAAE,EAAmB;YAAjB,OAAO,aAAA,EAAE,MAAM,YAAA;QAClC,IAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAC3C,IAAM,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAEvC,KAAK,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;YAC3C,IAAM,cAAc,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YACnC,IAAI,MAAM,CAAC,IAAI,EAAE,cAAc,CAAC;gBAAE,OAAO,IAAI,CAAC;YAC9C,IACI,OAAO,CAAC,KAAK,CAAC,cAAc,CAAC;gBAC7B,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,QAAQ,EAC9C;gBACE,MAAM;aACT;SACJ;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IACD,cAAc,YAAC,IAAI,EAAE,EAAmB;YAAjB,OAAO,aAAA,EAAE,MAAM,YAAA;QAClC,IAAM,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAEvC,OAAO,OAAO;aACT,WAAW,CAAC,IAAI,CAAC;aACjB,KAAK,CACF,UAAC,OAAO;YACJ,OAAA,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC;gBACrB,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC;gBACvB,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,QAAQ;QAFrC,CAEqC,CAC5C,CAAC;IACV,CAAC;IACD,YAAY,YAAC,IAAI,EAAE,EAAmB;YAAjB,OAAO,aAAA,EAAE,MAAM,YAAA;QAChC,OAAO,OAAO;aACT,WAAW,CAAC,IAAI,CAAC;aACjB,KAAK,CACF,UAAC,OAAO,IAAK,OAAA,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,EAAhD,CAAgD,CAChE,CAAC;IACV,CAAC;CACJ,CAAC;AAEF,SAAgB,gBAAgB,CAC5B,IAA6B,EAC7B,IAAY,EACZ,SAAiC,EACjC,QAAgB;IAEhB,IAAI,SAAS,KAAK,IAAI,EAAE;QACpB,IAAI,IAAI,CAAC,MAAM,GAAG,QAAQ,EAAE;YACxB,MAAM,IAAI,KAAK,CAAC,wBAAiB,IAAI,0BAAuB,CAAC,CAAC;SACjE;KACJ;SAAM,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE;QACjC,MAAM,IAAI,KAAK,CAAC,wBAAiB,IAAI,gCAA6B,CAAC,CAAC;KACvE;AACL,CAAC;AAbD,4CAaC"}