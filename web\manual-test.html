<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>闲鱼收益监控器 - 手动测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #333;
            margin: 0;
            font-size: 2.5em;
        }
        
        .header p {
            color: #666;
            margin: 10px 0 0 0;
            font-size: 1.1em;
        }
        
        .step {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            background: #f9f9f9;
        }
        
        .step h3 {
            margin-top: 0;
            color: #333;
            font-size: 1.3em;
        }
        
        .step.active {
            border-color: #667eea;
            background: #f0f4ff;
        }
        
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        
        .input-group {
            margin: 15px 0;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        
        .input-group input, .input-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        .input-group textarea {
            height: 100px;
            resize: vertical;
        }
        
        .qr-display {
            text-align: center;
            margin: 20px 0;
        }
        
        .qr-display img {
            max-width: 200px;
            max-height: 200px;
            border: 2px solid #ddd;
            border-radius: 10px;
        }
        
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .data-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        
        .data-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        
        .data-item:last-child {
            border-bottom: none;
        }
        
        .data-label {
            font-weight: bold;
            color: #333;
        }
        
        .data-value {
            font-size: 1.2em;
            color: #667eea;
            font-weight: bold;
        }
        
        .instructions {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .instructions h4 {
            margin-top: 0;
            color: #856404;
        }
        
        .instructions ol {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .instructions li {
            margin: 5px 0;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 闲鱼收益监控器</h1>
            <p>手动测试版本 - 完整流程演示</p>
        </div>
        
        <!-- 步骤1: 获取二维码 -->
        <div class="step active" id="step1">
            <h3>步骤1: 获取登录二维码</h3>
            
            <div class="instructions">
                <h4>📋 操作说明：</h4>
                <ol>
                    <li>在新标签页中打开 <a href="https://www.goofish.pro/login" target="_blank">https://www.goofish.pro/login</a></li>
                    <li>等待页面加载完成，二维码出现</li>
                    <li>右键点击二维码图片</li>
                    <li>选择"复制图片地址"或"检查元素"查看src属性</li>
                    <li>将完整的data:image/png;base64,... 地址粘贴到下面的输入框</li>
                </ol>
            </div>
            
            <div class="input-group">
                <label for="qrCodeInput">二维码Base64数据：</label>
                <textarea id="qrCodeInput" placeholder="粘贴二维码的完整data:image/png;base64,... 地址"></textarea>
            </div>
            
            <button class="btn" onclick="loadQRCode()">加载二维码</button>
            <button class="btn" onclick="testAutoQR()">自动获取二维码（测试）</button>
            
            <div id="qrDisplay" class="qr-display" style="display: none;">
                <img id="qrImage" alt="登录二维码">
                <p>请使用闲鱼APP扫描此二维码登录</p>
            </div>
            
            <div id="step1Status"></div>
        </div>
        
        <!-- 步骤2: 检查登录状态 -->
        <div class="step" id="step2">
            <h3>步骤2: 检查登录状态</h3>
            <p>扫描二维码后，点击下面的按钮检查登录状态：</p>
            
            <button class="btn" onclick="checkLoginStatus()" disabled id="checkLoginBtn">检查登录状态</button>
            <button class="btn" onclick="startAutoCheck()" disabled id="autoCheckBtn">开始自动检查</button>
            <button class="btn" onclick="stopAutoCheck()" disabled id="stopCheckBtn">停止自动检查</button>
            
            <div id="step2Status"></div>
        </div>
        
        <!-- 步骤3: 获取收益数据 -->
        <div class="step" id="step3">
            <h3>步骤3: 获取收益数据</h3>
            <p>登录成功后，获取您的收益数据：</p>
            
            <button class="btn" onclick="fetchEarningsData()" disabled id="fetchDataBtn">获取收益数据</button>
            <button class="btn" onclick="startAutoFetch()" disabled id="autoFetchBtn">开始自动获取</button>
            
            <div id="earningsDisplay" class="data-display" style="display: none;">
                <div class="data-item">
                    <span class="data-label">订单总数：</span>
                    <span class="data-value" id="orderCount">--</span>
                </div>
                <div class="data-item">
                    <span class="data-label">总收益：</span>
                    <span class="data-value" id="totalEarnings">-- 元</span>
                </div>
                <div class="data-item">
                    <span class="data-label">最后更新：</span>
                    <span class="data-value" id="lastUpdate">--</span>
                </div>
            </div>
            
            <div id="step3Status"></div>
        </div>
    </div>

    <script>
        let currentSessionId = null;
        let autoCheckInterval = null;
        let autoFetchInterval = null;
        
        // 显示状态消息
        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        // 加载二维码
        function loadQRCode() {
            const qrCodeData = document.getElementById('qrCodeInput').value.trim();
            
            if (!qrCodeData) {
                showStatus('step1Status', '请输入二维码数据', 'error');
                return;
            }
            
            if (!qrCodeData.startsWith('data:image/')) {
                showStatus('step1Status', '二维码数据格式不正确，应该以 data:image/ 开头', 'error');
                return;
            }
            
            // 显示二维码
            const qrImage = document.getElementById('qrImage');
            qrImage.src = qrCodeData;
            document.getElementById('qrDisplay').style.display = 'block';
            
            // 生成会话ID
            currentSessionId = 'manual_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            
            // 启用下一步
            document.getElementById('checkLoginBtn').disabled = false;
            document.getElementById('autoCheckBtn').disabled = false;
            document.getElementById('step2').classList.add('active');
            
            showStatus('step1Status', '二维码加载成功！会话ID: ' + currentSessionId, 'success');
        }
        
        // 测试自动获取二维码
        async function testAutoQR() {
            try {
                showStatus('step1Status', '正在尝试自动获取二维码...', 'info');
                
                const response = await fetch('/api/proxy/login');
                const data = await response.json();
                
                if (data.success && data.qrCode) {
                    document.getElementById('qrCodeInput').value = data.qrCode;
                    loadQRCode();
                    currentSessionId = data.sessionId;
                    showStatus('step1Status', '自动获取二维码成功！', 'success');
                } else {
                    showStatus('step1Status', '自动获取失败: ' + data.message + '\\n请手动获取二维码', 'error');
                }
            } catch (error) {
                showStatus('step1Status', '自动获取失败: ' + error.message, 'error');
            }
        }
        
        // 检查登录状态
        async function checkLoginStatus() {
            if (!currentSessionId) {
                showStatus('step2Status', '请先加载二维码', 'error');
                return;
            }
            
            try {
                showStatus('step2Status', '正在检查登录状态...', 'info');
                
                const response = await fetch('/api/check-login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ sessionId: currentSessionId })
                });
                
                const data = await response.json();
                
                if (data.success && data.isLoggedIn) {
                    showStatus('step2Status', '登录成功！', 'success');
                    
                    // 启用下一步
                    document.getElementById('fetchDataBtn').disabled = false;
                    document.getElementById('autoFetchBtn').disabled = false;
                    document.getElementById('step3').classList.add('active');
                    
                    // 停止自动检查
                    stopAutoCheck();
                } else {
                    showStatus('step2Status', '等待登录: ' + data.message, 'info');
                }
            } catch (error) {
                showStatus('step2Status', '检查登录状态失败: ' + error.message, 'error');
            }
        }
        
        // 开始自动检查登录状态
        function startAutoCheck() {
            if (autoCheckInterval) return;
            
            document.getElementById('autoCheckBtn').disabled = true;
            document.getElementById('stopCheckBtn').disabled = false;
            
            autoCheckInterval = setInterval(checkLoginStatus, 3000);
            showStatus('step2Status', '开始自动检查登录状态（每3秒检查一次）', 'info');
        }
        
        // 停止自动检查
        function stopAutoCheck() {
            if (autoCheckInterval) {
                clearInterval(autoCheckInterval);
                autoCheckInterval = null;
            }
            
            document.getElementById('autoCheckBtn').disabled = false;
            document.getElementById('stopCheckBtn').disabled = true;
        }
        
        // 获取收益数据
        async function fetchEarningsData() {
            if (!currentSessionId) {
                showStatus('step3Status', '请先完成登录', 'error');
                return;
            }
            
            try {
                showStatus('step3Status', '正在获取收益数据...', 'info');
                
                const response = await fetch(`/api/proxy/earnings?sessionId=${currentSessionId}`);
                const data = await response.json();
                
                if (data.success && data.data) {
                    // 显示数据
                    document.getElementById('orderCount').textContent = data.data.num || 0;
                    document.getElementById('totalEarnings').textContent = (data.data.account || 0) + ' 元';
                    document.getElementById('lastUpdate').textContent = new Date().toLocaleString();
                    document.getElementById('earningsDisplay').style.display = 'block';
                    
                    showStatus('step3Status', '收益数据获取成功！', 'success');
                } else {
                    showStatus('step3Status', '获取收益数据失败: ' + data.message, 'error');
                }
            } catch (error) {
                showStatus('step3Status', '获取收益数据失败: ' + error.message, 'error');
            }
        }
        
        // 开始自动获取收益数据
        function startAutoFetch() {
            if (autoFetchInterval) return;
            
            document.getElementById('autoFetchBtn').disabled = true;
            
            // 立即获取一次
            fetchEarningsData();
            
            // 每30秒自动获取
            autoFetchInterval = setInterval(fetchEarningsData, 30000);
            showStatus('step3Status', '开始自动获取收益数据（每30秒更新一次）', 'info');
        }
        
        // 页面卸载时清理定时器
        window.addEventListener('beforeunload', () => {
            stopAutoCheck();
            if (autoFetchInterval) {
                clearInterval(autoFetchInterval);
            }
        });
    </script>
</body>
</html>
