// 闲鱼收益监控器配置文件

const config = {
    // 服务器配置
    server: {
        port: process.env.PORT || 3000,
        host: process.env.HOST || 'localhost'
    },

    // 目标网站配置
    target: {
        loginUrl: 'https://www.goofish.pro/login',
        apiBaseUrl: 'https://api.goofish.pro/api',
        earningsEndpoint: '/stats/order'
    },

    // API参数配置
    apiParams: {
        channel: 1,
        version: '3.54.31',
        authorize_id: 0,
        date_type: 1,
        selectIndex: 0
    },

    // 请求配置
    request: {
        timeout: 10000,
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        headers: {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive'
        }
    },

    // 登录检查配置
    loginCheck: {
        interval: 3000, // 检查间隔（毫秒）
        maxAttempts: 100, // 最大检查次数
        timeout: 300000 // 总超时时间（5分钟）
    },

    // 数据刷新配置
    dataRefresh: {
        autoRefresh: false, // 是否自动刷新
        interval: 30000 // 自动刷新间隔（毫秒）
    },

    // 调试配置
    debug: {
        enabled: process.env.NODE_ENV !== 'production',
        logLevel: 'info', // error, warn, info, debug
        logRequests: true,
        logResponses: false
    },

    // 安全配置
    security: {
        sessionTimeout: 3600000, // 会话超时时间（1小时）
        maxSessions: 100, // 最大会话数
        rateLimitWindow: 60000, // 速率限制窗口（1分钟）
        rateLimitMax: 60 // 每个窗口最大请求数
    }
};

module.exports = config;
